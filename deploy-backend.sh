#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Default version
VERSION="Beta-1.0.0"

# Parse command line options
while getopts v: flag
do
    case "${flag}" in
        v) VERSION=${OPTARG};;
    esac
done

echo "Deploying version: $VERSION"

# --- Backend Deployment ---
echo "Building backend..."
cd app
wasp build
echo "Backend build complete."

echo "Building backend Docker image..."
docker build -t kotoubm7/dapi-saas:$VERSION .
echo "Backend Docker image build complete."

echo "Pushing backend Docker image..."
docker push kotoubm7/dapi-saas:$VERSION
echo "Backend Docker image push complete."

echo "Updating backend deployment image tag..."
# Note: Assuming the image line looks like 'image: kotoubm7/dapi-saas:...'
sed -i 's|image: kotoubm7/dapi-saas:.*|image: kotoubm7/dapi-saas:'"$VERSION"'|g' ../k8s-wasp/backend/05-app-deployment.yaml
echo "Backend deployment image tag updated."

echo "Deleting existing backend Kubernetes resources (if any)..."
kubectl delete --ignore-not-found=true -f ../k8s-wasp/backend
echo "Existing backend resources deleted (or none found)."

echo "Deploying backend Kubernetes manifests..."
kubectl apply -f ../k8s-wasp/backend
echo "Backend deployment complete."